# -*- coding: utf-8 -*-
"""
统一的产品知识库数据处理系统 - Streamlit应用
整合所有功能模块，提供统一的Web界面
"""

import streamlit as st
import asyncio
import logging
import traceback
from pathlib import Path
import sys
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 导入配置和模块
try:
    from unified_config import config_manager
    logging.info("统一配置加载成功")
except ImportError as e:
    st.error(f"配置加载失败: {e}")
    st.stop()

# 页面配置
st.set_page_config(
    page_title="产品知识库数据处理系统",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    .status-card {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #007bff;
        margin: 1rem 0;
    }
    .success-card {
        background: #d4edda;
        border-left-color: #28a745;
    }
    .warning-card {
        background: #fff3cd;
        border-left-color: #ffc107;
    }
    .error-card {
        background: #f8d7da;
        border-left-color: #dc3545;
    }
    .metric-card {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        text-align: center;
    }
</style>
""", unsafe_allow_html=True)

class UnifiedApp:
    """统一应用主类"""
    
    def __init__(self):
        self.config = config_manager
        self.setup_session_state()
        
    def setup_session_state(self):
        """初始化会话状态"""
        if 'initialized' not in st.session_state:
            st.session_state.initialized = True
            st.session_state.current_task = None
            st.session_state.task_progress = 0
            st.session_state.logs = []
            st.session_state.processing_stats = {}
            
    def render_header(self):
        """渲染页面头部"""
        st.markdown("""
        <div class="main-header">
            <h1>🤖 产品知识库数据处理系统</h1>
            <p>基于硅基流动大模型的智能知识库索引生成与FastGPT同步系统</p>
        </div>
        """, unsafe_allow_html=True)
    
    def render_sidebar(self):
        """渲染侧边栏"""
        with st.sidebar:
            st.title("🔧 系统控制")
            
            # 系统状态
            st.subheader("📊 系统状态")
            self.render_system_status()
            
            st.divider()
            
            # 快速操作
            st.subheader("⚡ 快速操作")
            
            col1, col2 = st.columns(2)
            with col1:
                if st.button("🔄 刷新状态", use_container_width=True):
                    st.rerun()
            
            with col2:
                if st.button("🧹 清理缓存", use_container_width=True):
                    self.clear_cache()
            
            st.divider()
            
            # 配置管理
            st.subheader("⚙️ 配置管理")
            if st.button("📝 编辑配置", use_container_width=True):
                st.session_state.show_config_editor = True
            
            if st.button("💾 保存配置", use_container_width=True):
                if self.config.save_config():
                    st.success("配置保存成功")
                else:
                    st.error("配置保存失败")
    
    def render_system_status(self):
        """渲染系统状态"""
        validation_results = self.config.validate_config()
        
        for service, is_valid in validation_results.items():
            if is_valid:
                st.success(f"✅ {service}")
            else:
                st.error(f"❌ {service}")
    
    def clear_cache(self):
        """清理缓存"""
        try:
            # 清理Streamlit缓存
            st.cache_data.clear()
            st.cache_resource.clear()
            
            # 清理会话状态中的缓存数据
            cache_keys = [key for key in st.session_state.keys() if key.startswith('cache_')]
            for key in cache_keys:
                del st.session_state[key]
            
            st.success("缓存清理完成")
        except Exception as e:
            st.error(f"缓存清理失败: {e}")
    
    def render_main_tabs(self):
        """渲染主要标签页"""
        tab1, tab2, tab3, tab4, tab5, tab6 = st.tabs([
            "🏠 仪表板", "📊 数据处理", "🔄 同步管理", 
            "🤖 AI增强", "⚙️ 配置管理", "📋 日志监控"
        ])
        
        with tab1:
            self.render_dashboard()
        
        with tab2:
            self.render_data_processing()
        
        with tab3:
            self.render_sync_management()
        
        with tab4:
            self.render_ai_enhancement()
        
        with tab5:
            self.render_config_management()
        
        with tab6:
            self.render_log_monitoring()
    
    def render_dashboard(self):
        """渲染仪表板"""
        st.header("📊 系统仪表板")
        
        # 系统概览
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.markdown("""
            <div class="metric-card">
                <h3>📁 数据源</h3>
                <h2>3</h2>
                <p>云商API + 本地文件 + 历史数据</p>
            </div>
            """, unsafe_allow_html=True)
        
        with col2:
            st.markdown("""
            <div class="metric-card">
                <h3>🔄 同步状态</h3>
                <h2>待检查</h2>
                <p>点击同步管理查看详情</p>
            </div>
            """, unsafe_allow_html=True)
        
        with col3:
            st.markdown("""
            <div class="metric-card">
                <h3>🤖 AI模型</h3>
                <h2>4</h2>
                <p>视觉 + 文本 + 代码 + 轻量级</p>
            </div>
            """, unsafe_allow_html=True)
        
        with col4:
            st.markdown("""
            <div class="metric-card">
                <h3>📈 处理进度</h3>
                <h2>0%</h2>
                <p>暂无正在进行的任务</p>
            </div>
            """, unsafe_allow_html=True)
        
        st.divider()
        
        # 快速开始
        st.subheader("🚀 快速开始")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("""
            ### 📋 推荐流程
            1. **配置检查**: 确保所有配置项正确
            2. **数据库初始化**: 创建必要的表结构
            3. **数据导入**: 导入历史数据和云商数据
            4. **AI增强处理**: 使用AI模型优化内容
            5. **同步到FastGPT**: 将处理结果同步到知识库
            """)
        
        with col2:
            st.markdown("""
            ### ⚠️ 注意事项
            - 确保数据库连接正常
            - 检查API密钥配置
            - 确认文件路径存在
            - 监控处理进度和日志
            - 定期备份重要数据
            """)
        
        # 系统健康检查
        st.subheader("🏥 系统健康检查")
        if st.button("🔍 执行健康检查", type="primary"):
            self.run_health_check()
    
    def render_data_processing(self):
        """渲染数据处理页面"""
        st.header("📊 数据处理中心")
        
        # 数据源选择
        st.subheader("📂 数据源管理")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.markdown("### 🌐 云商API")
            yunshang_enabled = st.checkbox("启用云商API数据获取", value=True)
            if yunshang_enabled:
                st.info("将从云商API获取最新产品数据")
                if st.button("🔄 测试API连接"):
                    self.test_yunshang_api()
        
        with col2:
            st.markdown("### 📁 本地文件")
            local_enabled = st.checkbox("启用本地文件扫描", value=True)
            if local_enabled:
                local_path = st.text_input(
                    "本地文件路径", 
                    value=self.config.file_paths.local_files_base
                )
                if st.button("📂 浏览文件"):
                    self.browse_local_files(local_path)
        
        with col3:
            st.markdown("### 📚 历史数据")
            legacy_enabled = st.checkbox("启用历史数据导入", value=True)
            if legacy_enabled:
                st.info("将导入allcollections.json中的历史数据")
                if st.button("📋 预览数据"):
                    self.preview_legacy_data()
        
        st.divider()
        
        # 处理控制
        st.subheader("🎛️ 处理控制")
        
        col1, col2 = st.columns([2, 1])
        
        with col1:
            # 处理选项
            st.markdown("#### 处理选项")
            batch_size = st.slider("批处理大小", 10, 500, self.config.process.batch_size)
            max_retries = st.slider("最大重试次数", 1, 10, self.config.process.max_retries)
            timeout = st.slider("超时时间(秒)", 10, 300, self.config.process.timeout)
        
        with col2:
            st.markdown("#### 执行操作")
            
            if st.button("🚀 开始完整处理", type="primary", use_container_width=True):
                self.start_full_processing(
                    yunshang_enabled, local_enabled, legacy_enabled,
                    batch_size, max_retries, timeout
                )
            
            if st.button("🔄 增量更新", use_container_width=True):
                self.start_incremental_update()
            
            if st.button("🛑 停止处理", use_container_width=True):
                self.stop_processing()
        
        # 处理进度
        if st.session_state.current_task:
            st.subheader("📈 处理进度")
            progress_bar = st.progress(st.session_state.task_progress)
            status_text = st.empty()
            
            # 实时更新进度
            self.update_progress_display(progress_bar, status_text)
    
    def render_sync_management(self):
        """渲染同步管理页面"""
        st.header("🔄 同步管理中心")
        
        # 同步状态概览
        st.subheader("📊 同步状态概览")
        
        # 这里应该从数据库获取实际数据
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("待审核", "0", "0")
        with col2:
            st.metric("已批准", "0", "0")
        with col3:
            st.metric("已同步", "0", "0")
        with col4:
            st.metric("同步失败", "0", "0")
        
        st.divider()
        
        # 变更检测
        st.subheader("🔍 变更检测")
        
        col1, col2 = st.columns([3, 1])
        
        with col1:
            st.info("变更检测将扫描所有数据源，识别新增、修改和删除的内容")
        
        with col2:
            if st.button("🔍 检测变更", type="primary", use_container_width=True):
                self.detect_changes()
        
        # 待审核列表
        st.subheader("📋 待审核变更")
        
        # 这里应该显示实际的待审核数据
        st.info("暂无待审核的变更")
        
        # 同步操作
        st.subheader("⚡ 同步操作")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("✅ 批量批准", use_container_width=True):
                self.batch_approve_changes()
        
        with col2:
            if st.button("🔄 执行同步", use_container_width=True):
                self.execute_sync()
        
        with col3:
            if st.button("📊 同步报告", use_container_width=True):
                self.generate_sync_report()
    
    def render_ai_enhancement(self):
        """渲染AI增强页面"""
        st.header("🤖 AI增强处理")
        
        # AI模型状态
        st.subheader("🧠 AI模型状态")
        
        models = self.config.siliconflow.models
        
        for model_type, model_name in models.items():
            with st.expander(f"📱 {model_type.title()} 模型: {model_name}"):
                col1, col2 = st.columns(2)
                
                with col1:
                    st.write(f"**模型名称**: {model_name}")
                    st.write(f"**类型**: {model_type}")
                    st.write("**状态**: 🟢 可用")
                
                with col2:
                    if st.button(f"🧪 测试 {model_type} 模型"):
                        self.test_ai_model(model_type)
        
        st.divider()
        
        # AI处理选项
        st.subheader("⚙️ AI处理选项")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("#### 图片处理")
            enable_image_analysis = st.checkbox("启用图片相关性分析", value=True)
            image_threshold = st.slider("相关性阈值", 0.0, 10.0, 6.0, 0.1)
            
            st.markdown("#### 内容优化")
            enable_content_optimization = st.checkbox("启用内容结构优化", value=True)
            enable_qa_generation = st.checkbox("启用问答对生成", value=True)
        
        with col2:
            st.markdown("#### 处理控制")
            max_concurrent = st.slider("最大并发请求", 1, 10, 3)
            enable_caching = st.checkbox("启用结果缓存", value=True)
            
            st.markdown("#### 成本控制")
            daily_limit = st.number_input("每日成本限制($)", 0.0, 1000.0, 50.0)
            monthly_limit = st.number_input("每月成本限制($)", 0.0, 10000.0, 1000.0)
        
        st.divider()
        
        # AI处理执行
        st.subheader("🚀 AI处理执行")
        
        if st.button("🤖 开始AI增强处理", type="primary"):
            self.start_ai_enhancement(
                enable_image_analysis, image_threshold,
                enable_content_optimization, enable_qa_generation,
                max_concurrent, enable_caching,
                daily_limit, monthly_limit
            )
    
    def run_health_check(self):
        """执行系统健康检查"""
        with st.spinner("正在执行健康检查..."):
            time.sleep(2)  # 模拟检查过程
            
            # 这里应该实现实际的健康检查逻辑
            st.success("✅ 系统健康检查完成")
            
            # 显示检查结果
            col1, col2 = st.columns(2)
            
            with col1:
                st.markdown("""
                **✅ 通过的检查:**
                - Python环境正常
                - 配置文件完整
                - 目录结构正确
                - 依赖包已安装
                """)
            
            with col2:
                st.markdown("""
                **⚠️ 需要注意:**
                - 数据库连接需要验证
                - API密钥需要配置
                - 文件路径需要确认
                """)
    
    def test_yunshang_api(self):
        """测试云商API连接"""
        with st.spinner("正在测试API连接..."):
            time.sleep(1)
            # 这里应该实现实际的API测试逻辑
            st.success("✅ API连接测试成功")
    
    def start_full_processing(self, yunshang_enabled, local_enabled, legacy_enabled, 
                            batch_size, max_retries, timeout):
        """开始完整处理"""
        st.session_state.current_task = "完整数据处理"
        st.session_state.task_progress = 0
        
        with st.spinner("正在启动完整处理..."):
            # 这里应该实现实际的处理逻辑
            st.success("✅ 完整处理已启动")
    
    def detect_changes(self):
        """检测变更"""
        with st.spinner("正在检测变更..."):
            time.sleep(2)
            # 这里应该实现实际的变更检测逻辑
            st.success("✅ 变更检测完成")
    
    def start_ai_enhancement(self, *args):
        """开始AI增强处理"""
        with st.spinner("正在启动AI增强处理..."):
            time.sleep(2)
            # 这里应该实现实际的AI处理逻辑
            st.success("✅ AI增强处理已启动")
    
    def test_ai_model(self, model_type):
        """测试AI模型"""
        with st.spinner(f"正在测试{model_type}模型..."):
            time.sleep(1)
            # 这里应该实现实际的模型测试逻辑
            st.success(f"✅ {model_type}模型测试成功")

    def render_config_management(self):
        """渲染配置管理页面"""
        st.header("⚙️ 配置管理")

        # 配置分类
        config_tab1, config_tab2, config_tab3, config_tab4 = st.tabs([
            "🗄️ 数据库", "🌐 API服务", "📁 文件路径", "🔧 系统设置"
        ])

        with config_tab1:
            self.render_database_config()

        with config_tab2:
            self.render_api_config()

        with config_tab3:
            self.render_file_path_config()

        with config_tab4:
            self.render_system_config()

    def render_database_config(self):
        """渲染数据库配置"""
        st.subheader("🗄️ 数据库配置")

        with st.form("database_config"):
            col1, col2 = st.columns(2)

            with col1:
                host = st.text_input("主机地址", value=self.config.database.host)
                port = st.number_input("端口", value=self.config.database.port, min_value=1, max_value=65535)
                user = st.text_input("用户名", value=self.config.database.user)

            with col2:
                password = st.text_input("密码", value=self.config.database.password, type="password")
                dbname = st.text_input("数据库名", value=self.config.database.dbname)

            col1, col2, col3 = st.columns(3)

            with col1:
                if st.form_submit_button("💾 保存配置", type="primary"):
                    self.config.database.host = host
                    self.config.database.port = port
                    self.config.database.user = user
                    self.config.database.password = password
                    self.config.database.dbname = dbname

                    if self.config.save_config():
                        st.success("数据库配置保存成功")
                    else:
                        st.error("数据库配置保存失败")

            with col2:
                if st.form_submit_button("🔍 测试连接"):
                    self.test_database_connection(host, port, user, password, dbname)

            with col3:
                if st.form_submit_button("🏗️ 初始化数据库"):
                    self.initialize_database()

    def render_api_config(self):
        """渲染API配置"""
        st.subheader("🌐 API服务配置")

        # 硅基流动配置
        with st.expander("🤖 硅基流动配置", expanded=True):
            with st.form("siliconflow_config"):
                api_key = st.text_input(
                    "API密钥",
                    value=self.config.siliconflow.api_key,
                    type="password"
                )
                api_base_url = st.text_input(
                    "API基础URL",
                    value=self.config.siliconflow.api_base_url
                )

                if st.form_submit_button("💾 保存硅基流动配置"):
                    self.config.siliconflow.api_key = api_key
                    self.config.siliconflow.api_base_url = api_base_url

                    if self.config.save_config():
                        st.success("硅基流动配置保存成功")
                    else:
                        st.error("硅基流动配置保存失败")

        # FastGPT配置
        with st.expander("📚 FastGPT配置", expanded=True):
            with st.form("fastgpt_config"):
                fastgpt_api_url = st.text_input(
                    "API地址",
                    value=self.config.fastgpt.api_url
                )
                fastgpt_api_key = st.text_input(
                    "API密钥",
                    value=self.config.fastgpt.api_key,
                    type="password"
                )
                fastgpt_dataset_id = st.text_input(
                    "数据集ID",
                    value=self.config.fastgpt.dataset_id
                )

                if st.form_submit_button("💾 保存FastGPT配置"):
                    self.config.fastgpt.api_url = fastgpt_api_url
                    self.config.fastgpt.api_key = fastgpt_api_key
                    self.config.fastgpt.dataset_id = fastgpt_dataset_id

                    if self.config.save_config():
                        st.success("FastGPT配置保存成功")
                    else:
                        st.error("FastGPT配置保存失败")

        # 云商API配置
        with st.expander("🏢 云商API配置", expanded=False):
            with st.form("yunshang_config"):
                yunshang_base_url = st.text_input(
                    "API基础URL",
                    value=self.config.api.base_url
                )
                yunshang_username = st.text_input(
                    "用户名",
                    value=self.config.api.username
                )
                yunshang_password = st.text_input(
                    "密码",
                    value=self.config.api.password,
                    type="password"
                )

                if st.form_submit_button("💾 保存云商API配置"):
                    self.config.api.base_url = yunshang_base_url
                    self.config.api.username = yunshang_username
                    self.config.api.password = yunshang_password

                    if self.config.save_config():
                        st.success("云商API配置保存成功")
                    else:
                        st.error("云商API配置保存失败")

    def render_file_path_config(self):
        """渲染文件路径配置"""
        st.subheader("📁 文件路径配置")

        with st.form("file_path_config"):
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("#### 数据文件")
                allcollections = st.text_input(
                    "历史数据文件",
                    value=self.config.file_paths.allcollections
                )
                product_structure = st.text_input(
                    "产品结构文件",
                    value=self.config.file_paths.product_structure
                )
                local_files_base = st.text_input(
                    "本地文件基础路径",
                    value=self.config.file_paths.local_files_base
                )

            with col2:
                st.markdown("#### 工作目录")
                logs_dir = st.text_input(
                    "日志目录",
                    value=self.config.file_paths.logs_dir
                )
                downloads_dir = st.text_input(
                    "下载目录",
                    value=self.config.file_paths.downloads_dir
                )
                uploads_dir = st.text_input(
                    "上传目录",
                    value=self.config.file_paths.uploads_dir
                )
                temp_dir = st.text_input(
                    "临时目录",
                    value=self.config.file_paths.temp_dir
                )
                results_dir = st.text_input(
                    "结果目录",
                    value=self.config.file_paths.results_dir
                )

            col1, col2 = st.columns(2)

            with col1:
                if st.form_submit_button("💾 保存路径配置", type="primary"):
                    self.config.file_paths.allcollections = allcollections
                    self.config.file_paths.product_structure = product_structure
                    self.config.file_paths.local_files_base = local_files_base
                    self.config.file_paths.logs_dir = logs_dir
                    self.config.file_paths.downloads_dir = downloads_dir
                    self.config.file_paths.uploads_dir = uploads_dir
                    self.config.file_paths.temp_dir = temp_dir
                    self.config.file_paths.results_dir = results_dir

                    if self.config.save_config():
                        st.success("文件路径配置保存成功")
                    else:
                        st.error("文件路径配置保存失败")

            with col2:
                if st.form_submit_button("📂 创建目录"):
                    self.config.create_directories()
                    st.success("目录创建完成")

    def render_system_config(self):
        """渲染系统配置"""
        st.subheader("🔧 系统设置")

        with st.form("system_config"):
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("#### 处理设置")
                batch_size = st.slider(
                    "批处理大小",
                    10, 1000,
                    self.config.process.batch_size
                )
                max_retries = st.slider(
                    "最大重试次数",
                    1, 10,
                    self.config.process.max_retries
                )
                timeout = st.slider(
                    "超时时间(秒)",
                    10, 300,
                    self.config.process.timeout
                )
                max_concurrent = st.slider(
                    "最大并发请求",
                    1, 20,
                    self.config.process.max_concurrent_requests
                )

            with col2:
                st.markdown("#### 功能开关")
                enable_local_scan = st.checkbox(
                    "启用本地文件扫描",
                    value=self.config.process.enable_local_scan
                )
                enable_yunshang_api = st.checkbox(
                    "启用云商API",
                    value=self.config.process.enable_yunshang_api
                )
                enable_legacy_import = st.checkbox(
                    "启用历史数据导入",
                    value=self.config.process.enable_legacy_import
                )
                cache_enabled = st.checkbox(
                    "启用缓存",
                    value=self.config.process.cache_enabled
                )
                cache_ttl = st.number_input(
                    "缓存生存时间(秒)",
                    60, 86400,
                    self.config.process.cache_ttl
                )

            if st.form_submit_button("💾 保存系统配置", type="primary"):
                self.config.process.batch_size = batch_size
                self.config.process.max_retries = max_retries
                self.config.process.timeout = timeout
                self.config.process.max_concurrent_requests = max_concurrent
                self.config.process.enable_local_scan = enable_local_scan
                self.config.process.enable_yunshang_api = enable_yunshang_api
                self.config.process.enable_legacy_import = enable_legacy_import
                self.config.process.cache_enabled = cache_enabled
                self.config.process.cache_ttl = cache_ttl

                if self.config.save_config():
                    st.success("系统配置保存成功")
                else:
                    st.error("系统配置保存失败")

    def render_log_monitoring(self):
        """渲染日志监控页面"""
        st.header("📋 日志监控")

        # 日志级别选择
        col1, col2, col3 = st.columns([2, 1, 1])

        with col1:
            log_level = st.selectbox(
                "日志级别",
                ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
                index=1
            )

        with col2:
            auto_refresh = st.checkbox("自动刷新", value=False)

        with col3:
            if st.button("🔄 刷新日志"):
                st.rerun()

        # 日志显示
        st.subheader("📄 实时日志")

        # 这里应该读取实际的日志文件
        log_content = self.get_recent_logs(log_level)

        if log_content:
            st.code(log_content, language="text")
        else:
            st.info("暂无日志内容")

        # 日志统计
        st.subheader("📊 日志统计")

        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("总日志条数", "0")
        with col2:
            st.metric("错误数", "0", "0")
        with col3:
            st.metric("警告数", "0", "0")
        with col4:
            st.metric("今日日志", "0")

        # 自动刷新
        if auto_refresh:
            time.sleep(5)
            st.rerun()

    def test_database_connection(self, host, port, user, password, dbname):
        """测试数据库连接"""
        with st.spinner("正在测试数据库连接..."):
            try:
                # 这里应该实现实际的数据库连接测试
                time.sleep(1)
                st.success("✅ 数据库连接测试成功")
            except Exception as e:
                st.error(f"❌ 数据库连接测试失败: {e}")

    def initialize_database(self):
        """初始化数据库"""
        with st.spinner("正在初始化数据库..."):
            try:
                # 这里应该实现实际的数据库初始化逻辑
                time.sleep(2)
                st.success("✅ 数据库初始化成功")
            except Exception as e:
                st.error(f"❌ 数据库初始化失败: {e}")

    def get_recent_logs(self, level):
        """获取最近的日志"""
        try:
            log_file = Path(self.config.log.file)
            if log_file.exists():
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    # 返回最后50行
                    return ''.join(lines[-50:])
            else:
                return "日志文件不存在"
        except Exception as e:
            return f"读取日志失败: {e}"

    def browse_local_files(self, path):
        """浏览本地文件"""
        try:
            file_path = Path(path)
            if file_path.exists():
                files = list(file_path.glob("**/*"))[:100]  # 限制显示100个文件
                st.write(f"找到 {len(files)} 个文件:")
                for file in files:
                    st.write(f"📄 {file}")
            else:
                st.error("路径不存在")
        except Exception as e:
            st.error(f"浏览文件失败: {e}")

    def preview_legacy_data(self):
        """预览历史数据"""
        try:
            legacy_file = Path(self.config.file_paths.allcollections)
            if legacy_file.exists():
                with open(legacy_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    st.write(f"历史数据包含 {len(data)} 条记录")
                    if data:
                        st.json(data[0])  # 显示第一条记录作为示例
            else:
                st.error("历史数据文件不存在")
        except Exception as e:
            st.error(f"预览历史数据失败: {e}")

    def update_progress_display(self, progress_bar, status_text):
        """更新进度显示"""
        if st.session_state.current_task:
            progress_bar.progress(st.session_state.task_progress)
            status_text.text(f"当前任务: {st.session_state.current_task}")

    def stop_processing(self):
        """停止处理"""
        st.session_state.current_task = None
        st.session_state.task_progress = 0
        st.success("处理已停止")

    def start_incremental_update(self):
        """开始增量更新"""
        st.session_state.current_task = "增量更新"
        st.session_state.task_progress = 0
        st.success("增量更新已启动")

    def batch_approve_changes(self):
        """批量批准变更"""
        with st.spinner("正在批量批准变更..."):
            time.sleep(1)
            st.success("批量批准完成")

    def execute_sync(self):
        """执行同步"""
        with st.spinner("正在执行同步..."):
            time.sleep(2)
            st.success("同步执行完成")

    def generate_sync_report(self):
        """生成同步报告"""
        with st.spinner("正在生成同步报告..."):
            time.sleep(1)
            st.success("同步报告生成完成")

def main():
    """主函数"""
    try:
        app = UnifiedApp()

        # 渲染页面
        app.render_header()
        app.render_sidebar()
        app.render_main_tabs()

    except Exception as e:
        st.error(f"应用启动失败: {e}")
        st.code(traceback.format_exc())

if __name__ == "__main__":
    main()
